import { useContext } from 'react';
import { UserContext } from '../context/UserContext';
import { Link } from 'react-router';

export default function Friends() {
  const { users, user, followingOrfollowers } = useContext(UserContext);

  return (
    <div className="max-w-md mx-auto mt-10 bg-white rounded-xl shadow-lg p-8">
      <h2 className="mb-8 text-primary text-center text-3xl font-bold tracking-tight">
        Suggested Friends
      </h2>
      <ul className="space-y-6">
        {users.map((friend) => (
          <li
            key={friend._id}
            className="flex items-center justify-between bg-gray-50 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <Link to={`/other-profile/${friend._id}`}>
                <img
                  src={friend.profileImage}
                  alt={friend.fullName}
                  className="w-14 h-14 rounded-full mr-5 border-2 border-primary object-cover"
                />
              </Link>
              <span className="text-lg font-medium text-gray-800">{friend.fullName}</span>
            </div>

            <button
              onClick={() => {
                followingOrfollowers(friend._id);
              }}
              className="px-4 py-2 bg-primary text-white rounded-md shadow hover:bg-primary-dark transition-colors"
            >
              {user.following.includes(friend._id) ? 'Unfollow' : 'Follow'}
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}
