import cloudinary from '../configs/cloudinary.config.js';
import multer from 'multer';
import { CloudinaryStorage } from 'multer-storage-cloudinary';

const profileStorage = new CloudinaryStorage({
  cloudinary,
  params: {
    folder: 'profile',
    allowed_formats: ['jpg', 'png', 'jpeg'],
    transformation: [{ width: 500, height: 500, crop: 'limit' }],
  },
});

const postStorage = new CloudinaryStorage({
  cloudinary,
  params: {
    folder: 'posts',
    allowed_formats: ['jpg', 'png', 'jpeg'],
    transformation: [{ width: 800, height: 800, crop: 'limit' }],
  },
});

export const profileUpload = multer({ storage: profileStorage });
export const postUpload = multer({ storage: postStorage });
