import { useState, useContext } from 'react';
import { AuthContext } from './../context/AuthContext';
import { Link } from 'react-router';

export default function Login() {
  const { login } = useContext(AuthContext);

  const [loginData, setloginData] = useState({
    email: '',
    password: '',
  });

  const submitLoginData = async (e) => {
    e.preventDefault();
    await login(loginData);
    setloginData({
      email: '',
      password: '',
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center text-text-secondary">
      <div className="bg-white p-10 rounded-2xl shadow-2xl w-full max-w-md border border-gray-100">
        <h2 className="text-3xl font-extrabold mb-8 text-center text-primary tracking-tight">
          Create Your Account
        </h2>
        <form onSubmit={submitLoginData} className="space-y-6">
          <div>
            <label className="block text-gray-700 font-medium mb-2" htmlFor="email">
              Email
            </label>
            <input
              id="email"
              type="email"
              onChange={(e) => setloginData({ ...loginData, email: e.target.value })}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-gray-50 transition"
              placeholder="Enter your email"
              autoComplete="email"
            />
          </div>
          <div>
            <label className="block text-gray-700 font-medium mb-2" htmlFor="password">
              Password
            </label>
            <input
              id="password"
              type="password"
              onChange={(e) => setloginData({ ...loginData, password: e.target.value })}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-gray-50 transition"
              placeholder="Enter your password"
              autoComplete="new-password"
            />
          </div>

          <button
            type="submit"
            className="w-full bg-primary text-white py-3 rounded-lg font-semibold text-lg hover:bg-hover transition shadow-md"
          >
            Login
          </button>
        </form>
        <p className="mt-6 text-center text-gray-500 text-sm">
          Don't have an account? <span className="text-gray-400">Create one now!</span>{' '}
          <Link to="/register" className="text-primary font-medium hover:underline">
            Register
          </Link>
        </p>
      </div>
    </div>
  );
}
