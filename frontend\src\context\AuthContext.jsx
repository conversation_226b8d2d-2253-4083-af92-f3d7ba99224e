import { createContext, useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import axios from 'axios';

export const AuthContext = createContext();

export const AuthContextProvider = ({ children }) => {
  const URL = import.meta.env.VITE_BACKEND_URL;
  const [authenticate, setAuthenticate] = useState(false);

  const register = async (registerData) => {
    try {
      const { data } = await axios.post(`${URL}/auth/register`, registerData, {
        withCredentials: true,
      });

      setAuthenticate(true);
      toast.success(data.message);
    } catch (error) {
      toast.error(error.response.data.message);
    }
  };

  const login = async (loginData) => {
    try {
      const { data } = await axios.post(`${URL}/auth/login`, loginData, { withCredentials: true });
      setAuthenticate(true);
      toast.success(data.message);
    } catch (error) {
      toast.error(error.response.data.message);
    }
  };

  const logout = async () => {
    try {
      await axios.post(`${URL}/auth/logout`, {}, { withCredentials: true });

      setAuthenticate(false);
      toast.success('Logout successful');
    } catch (error) {
      toast.error(error.response.data.message);
    }
  };

  const isAuthenticate = async () => {
    try {
      const { data } = await axios.get(`${URL}/auth/authenticate`, { withCredentials: true });
      setAuthenticate(data.authenticated);
    } catch (error) {
      setAuthenticate(false);
      console.log(error.message);
    }
  };

  useEffect(() => {
    isAuthenticate();
  }, []);

  return (
    <AuthContext.Provider value={{ register, login, logout, authenticate }}>
      {children}
    </AuthContext.Provider>
  );
};
