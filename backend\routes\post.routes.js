import authenticate from '../middlewares/authenticate.js';
import { postUpload } from '../middlewares/multer.middleware.js';
import {
  createPost,
  likePost,
  deletePost,
  postComment,
  getAllPosts,
  getPost,
} from '../controllers/post.controllers.js';
import express from 'express';

const postRouter = express.Router();

// Route to create a new post
postRouter.post('/create', authenticate, postUpload.single('image'), createPost);

// Route to like a post
postRouter.post('/like/:postId', authenticate, likePost);
// Route to delete a post
postRouter.delete('/delete/:postId', authenticate, deletePost);

// Route to comment on a post
postRouter.post('/comment/:postId', authenticate, postComment);

// Route to get all posts
postRouter.get('/all', authenticate, getAllPosts);

// Route to get a single post by ID
postRouter.get('/:postId', authenticate, getPost);

export default postRouter;
