import Post from '../models/post.models.js';
import User from '../models/user.models.js';

export const createPost = async (req, res) => {
  try {
    const { content } = req.body;
    const userId = req.user.id;

    if (!content) {
      return res.status(400).json({ message: 'Title and content are required.' });
    }

    const newPost = new Post({
      content,
      image: req.file ? req.file.path : '',
      userId,
    });

    const savedPost = await newPost.save();
    const user = await User.findById(userId);
    await user.userPosts.push(savedPost._id);
    await user.save();

    return res.status(201).json({
      message: 'Post created successfully.',
      post: savedPost,
    });
  } catch (error) {
    console.error('Error creating post:', error);
    return res.status(500).json({ message: 'Internal server error.' });
  }
};

export const likePost = async (req, res) => {
  try {
    const { postId } = req.params;
    const userId = req.user.id;

    const post = await Post.findById(postId);
    if (!post) {
      return res.status(404).json({ message: 'Post not found.' });
    }
    const alredayLiked = post.likes.includes(userId);

    const updateOperator = alredayLiked ? '$pull' : '$push';

    await Post.findByIdAndUpdate(postId, { [updateOperator]: { likes: userId } }, { new: true });

    const updatedPost = await Post.findById(postId).populate('likes', 'fullName profileImage');
    return res.status(200).json({
      message: alredayLiked ? 'Post unliked successfully.' : 'Post liked successfully.',
      post: updatedPost,
    });
  } catch (error) {
    console.error('Error liking post:', error);
    return res.status(500).json({ message: 'Internal server error.' });
  }
};

export const postComment = async (req, res) => {
  try {
    const { postId } = req.params;
    const { commentText } = req.body;
    const userId = req.user.id;

    if (!commentText) {
      return res.status(400).json({ message: 'Comment text is required.' });
    }

    const post = await Post.findById(postId);
    if (!post) {
      return res.status(404).json({ message: 'Post not found.' });
    }

    post.comments.push({
      userId,
      commentText,
      createdAt: new Date(),
    });

    const updatedPost = await post.save();
    return res.status(200).json({
      message: 'Comment added successfully.',
      post: updatedPost,
    });
  } catch (error) {
    console.error('Error adding comment:', error);
    return res.status(500).json({ message: 'Internal server error.' });
  }
};

export const deletePost = async (req, res) => {
  try {
    const { postId } = req.params;
    const userId = req.user.id;

    const post = await Post.findById(postId);
    if (!post) {
      return res.status(404).json({ message: 'Post not found.' });
    }
    if (post.userId.toString() !== userId) {
      return res.status(403).json({ message: 'You are not authorized to delete this post.' });
    }
    await Post.findByIdAndDelete(postId);
    const user = await User.findById(userId);
    user.userPosts.pull(postId);
    await user.save();
    return res.status(200).json({ message: 'Post deleted successfully.' });
  } catch (error) {
    console.error('Error deleting post:', error);
    return res.status(500).json({ message: 'Internal server error.' });
  }
};

export const getPost = async (req, res) => {
  try {
    const { postId } = req.params;

    const post = await Post.findById(postId)
      .populate('userId', 'fullName profileImage')
      .populate('comments.userId', 'fullName profileImage');
    if (!post) {
      return res.status(404).json({ message: 'Post not found.' });
    }

    return res.status(200).json({
      message: 'Post fetched successfully.',
      post,
    });
  } catch (error) {
    console.error('Error fetching post:', error);
    return res.status(500).json({ message: 'Internal server error.' });
  }
};

export const getAllPosts = async (req, res) => {
  try {
    const posts = await Post.find()
      .populate('userId', 'fullName profileImage')
      .populate('likes', 'fullName profileImage')
      .populate('comments.userId', 'fullName profileImage')
      .sort({ createdAt: -1 });

    return res.status(200).json({
      message: 'Posts fetched successfully.',
      posts,
    });
  } catch (error) {
    console.error('Error fetching posts:', error);
    return res.status(500).json({ message: 'Internal server error.' });
  }
};
