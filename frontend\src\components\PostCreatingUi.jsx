import { Link } from 'react-router';
import { useContext } from 'react';
import { UserContext } from '../context/UserContext';

export default function PostCreatingUi() {
  const { user } = useContext(UserContext);

  return (
    <div className="w-full max-w-xl mx-auto bg-white rounded-xl shadow-md p-6 flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <div className="w-14 h-14 rounded-full overflow-hidden border-2 border-primary">
          <img src={user?.profileImage} alt="Profile" className="w-full h-full object-cover" />
        </div>
        <Link to="/create-post" className="flex-1">
          <input
            type="text"
            placeholder="What's on your mind?"
            className="flex-1 bg-gray-100 rounded-full px-5 py-3 outline-none text-base text-gray-700 focus
            :bg-gray-200 transition"
          />
        </Link>
      </div>
      <div className="flex justify-end">
        <Link
          to="/create-post"
          className="bg-primary text-white px-6 py-2 rounded-full font-semibold shadow hover:bg-primary-dark transition"
        >
          Create Post
        </Link>
      </div>
    </div>
  );
}
