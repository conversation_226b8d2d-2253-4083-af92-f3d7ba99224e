import authenticate from '../middlewares/authenticate.js';
import express from 'express';
import {
  updateProfile,
  followingOrfollowers,
  getAllUsers,
  getCurrentUserProfile,
  getOtherProfile,
} from '../controllers/user.controllers.js';

const userRouter = express.Router();

// Route to update user profile
userRouter.put('/profile-update', authenticate, updateProfile);

// Route to follow or unfollow a user
userRouter.post('/follow/:id', authenticate, followingOrfollowers);
// Route to get all users
userRouter.get('/users', authenticate, getAllUsers);

// Route to get a specific user's profile
userRouter.get('/profile/:id', authenticate, getOtherProfile);

// Route to get the current user's profile
userRouter.get('/profile', authenticate, getCurrentUserProfile);

export default userRouter;
