import { useState } from 'react';
import { <PERSON><PERSON>illLike, AiFillMessage } from 'react-icons/ai';

const mockUser = {
  name: '<PERSON>',
  image: 'https://randomuser.me/api/portraits/women/44.jpg',
};

export default function Post() {
  const [showComments, setShowComments] = useState(false);
  const [commentInput, setCommentInput] = useState('');
  const [comments, setComments] = useState([
    {
      user: {
        name: 'Alice',
        image: 'https://randomuser.me/api/portraits/women/68.jpg',
      },
      text: 'Great post!',
    },
    {
      user: {
        name: '<PERSON>',
        image: 'https://randomuser.me/api/portraits/men/32.jpg',
      },
      text: 'Nice picture!',
    },
  ]);

  const handleCommentSubmit = (e) => {
    e.preventDefault();
    if (!commentInput.trim()) return;
    setComments([
      ...comments,
      {
        user: mockUser,
        text: commentInput,
      },
    ]);
    setCommentInput('');
  };

  return (
    <div className="w-full min-h-screen py-16 text-text-secondary">
      <div className="max-w-2xl mx-auto bg-white rounded-3xl shadow-2xl p-8 flex flex-col gap-8 border border-gray-100">
        {/* ...profile and post content... */}
        <div className="flex items-center gap-4 mb-4 border-b border-gray-200 pb-4">
          <img
            src="https://images.unsplash.com/photo-1502685104226-1c2b0f8d3e4f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=60"
            alt="Profile"
            className="w-14 h-14 rounded-full object-cover border-2 border-primary"
          />
          <div>
            <h2 className="text-lg font-bold text-gray-800">John Doe</h2>
            <p className="text-gray-400 text-xs">Posted on October 1, 2023</p>
          </div>
        </div>

        <div className="mb-4">
          <p className="text-gray-700 text-base leading-relaxed">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
            incididunt ut labore et dolore magna aliqua.
          </p>
        </div>

        <div className="mb-4">
          <img
            src="https://images.unsplash.com/photo-1502685104226-1c2b0f8d3e4f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=60"
            alt="Post"
            className="w-full h-64 object-cover rounded-xl shadow-lg"
          />
        </div>

        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center gap-2">
            <button className="flex items-center gap-1 text-primary hover:text-primary-dark transition text-lg font-semibold">
              <AiFillLike className="text-2xl" />
              <span>12</span>
            </button>
            <span className="text-gray-500 text-sm">Likes</span>
          </div>
          <div className="flex items-center gap-2">
            <button
              className="flex items-center gap-1 text-gray-500 hover:text-primary transition text-lg font-semibold"
              onClick={() => setShowComments((v) => !v)}
            >
              <AiFillMessage className="text-2xl" />
              <span>{comments.length}</span>
            </button>
            <span className="text-gray-500 text-sm">Comments</span>
          </div>
        </div>

        {showComments && (
          <div className="mt-6">
            <form onSubmit={handleCommentSubmit} className="flex gap-2 mb-4">
              <img
                src={mockUser.image}
                alt={mockUser.name}
                className="w-10 h-10 rounded-full object-cover border"
              />
              <input
                type="text"
                className="flex-1 border rounded-lg px-3 py-2"
                placeholder="Write a comment..."
                value={commentInput}
                onChange={(e) => setCommentInput(e.target.value)}
              />
              <button type="submit" className="bg-primary text-white px-4 py-2 rounded-lg">
                Post
              </button>
            </form>
            <div className="flex flex-col gap-4">
              {comments.map((c, idx) => (
                <div key={idx} className="flex items-start gap-3">
                  <img
                    src={c.user.image}
                    alt={c.user.name}
                    className="w-9 h-9 rounded-full object-cover border"
                  />
                  <div>
                    <div className="font-semibold text-gray-800">{c.user.name}</div>
                    <div className="text-gray-700">{c.text}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
