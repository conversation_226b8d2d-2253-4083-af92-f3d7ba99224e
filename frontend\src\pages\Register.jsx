import { useState, useContext } from 'react';
import { Link } from 'react-router';
import { AuthContext } from './../context/AuthContext';
export default function Register() {
  const { register } = useContext(AuthContext);
  const [registerData, setRegisterData] = useState({
    fullName: '',
    email: '',
    password: '',
    profileImage: '',
  });

  const formData = new FormData();
  formData.append('fullName', registerData.fullName);
  formData.append('email', registerData.email);
  formData.append('password', registerData.password);
  formData.append('profileImage', registerData.profileImage);

  const submitRegisterData = async (e) => {
    e.preventDefault();
    await register(formData);
    setRegisterData({
      fullName: '',
      email: '',
      password: '',
      profileImage: '',
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center text-text-secondary">
      <div className="bg-white p-10 rounded-2xl shadow-2xl w-full max-w-md border border-gray-100">
        <h2 className="text-3xl font-extrabold mb-8 text-center text-primary tracking-tight">
          Create Your Account
        </h2>
        <form onSubmit={submitRegisterData} className="space-y-6">
          <div>
            <label className="block text-gray-700 font-medium mb-2" htmlFor="fullName">
              Full Name
            </label>
            <input
              id="fullName"
              type="text"
              onChange={(e) => setRegisterData({ ...registerData, fullName: e.target.value })}
              value={registerData.fullName}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-gray-50 transition"
              placeholder="Enter your full name"
              autoComplete="name"
            />
          </div>
          <div>
            <label className="block text-gray-700 font-medium mb-2" htmlFor="email">
              Email
            </label>
            <input
              id="email"
              type="email"
              onChange={(e) => setRegisterData({ ...registerData, email: e.target.value })}
              value={registerData.email}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-gray-50 transition"
              placeholder="Enter your email"
              autoComplete="email"
            />
          </div>
          <div>
            <label className="block text-gray-700 font-medium mb-2" htmlFor="password">
              Password
            </label>
            <input
              id="password"
              type="password"
              onChange={(e) => setRegisterData({ ...registerData, password: e.target.value })}
              value={registerData.password}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-gray-50 transition"
              placeholder="Enter your password"
              autoComplete="new-password"
            />
          </div>
          <div>
            <label className="block text-gray-700 font-medium mb-2" htmlFor="profileImage">
              Profile Image
            </label>
            <input
              id="profileImage"
              type="file"
              onChange={(e) =>
                setRegisterData({ ...registerData, profileImage: e.target.files[0] })
              }
              className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-gray-50 transition file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:bg-primary file:text-white hover:file:bg-hover"
              accept="image/*"
            />
          </div>
          <button
            type="submit"
            className="w-full bg-primary text-white py-3 rounded-lg font-semibold text-lg hover:bg-hover transition shadow-md"
          >
            Register
          </button>
        </form>
        <p className="mt-6 text-center text-gray-500 text-sm">
          Already have an account?{' '}
          <Link to="/login" className="text-primary font-medium hover:underline">
            Login
          </Link>
        </p>
      </div>
    </div>
  );
}
