{"name": "backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"build": "echo \"Error: no build specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.2", "multer": "^2.0.1", "multer-storage-cloudinary": "^4.0.0", "nodemon": "^3.1.10"}}