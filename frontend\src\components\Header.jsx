import { useState, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import { FaUserFriends } from 'react-icons/fa';
import { GoHomeFill } from 'react-icons/go';
import { FiAlignJustify } from 'react-icons/fi';
import { IoMdClose } from 'react-icons/io';
import { FaCircleUser } from 'react-icons/fa6';
import { NavLink } from 'react-router';
import Button from './Button';

export default function Header() {
  const { authenticate, logout } = useContext(AuthContext);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <>
      <div className="w-full h-20 flex items-center justify-between px-10 ">
        <div className="Logo">
          <NavLink to="/" className="font-medium text-2xl">
            Social Media
          </NavLink>
        </div>

        {/* Desktop Navbar */}
        <div className="hidden md:flex nav space-x-24">
          <NavLink
            className={({ isActive }) => (isActive ? 'text-primary' : 'text-text-primary')}
            to="/"
          >
            <button>
              <GoHomeFill size={30} className=" hover:text-primary" />
            </button>
          </NavLink>
          <NavLink
            to="/friends"
            className={({ isActive }) => (isActive ? 'text-primary' : 'text-text-primary')}
          >
            <button>
              <FaUserFriends size={32} />
            </button>
          </NavLink>
          <NavLink
            to="/profile"
            className={({ isActive }) => (isActive ? 'text-primary' : 'text-text-primary')}
          >
            <button>
              <FaCircleUser size={28} />
            </button>
          </NavLink>
        </div>

        {/* Desktop Authentication */}
        <div className="hidden md:flex authenticationButton space-x-2.5">
          {authenticate ? (
            <NavLink onClick={() => logout()} to="/logout" className="loginButton">
              <Button text={'Logout'} />
            </NavLink>
          ) : (
            <>
              <NavLink to="/login" className="loginButton">
                <Button text={'Login'} />
              </NavLink>
              <NavLink to="/register" className="registerButton">
                <Button text={'Register'} />
              </NavLink>
            </>
          )}
        </div>

        {/* Mobile hamburger toggle */}
        <div className="md:hidden">
          {mobileMenuOpen ? (
            <button className=" cursor-pointer" onClick={() => setMobileMenuOpen((prev) => !prev)}>
              <IoMdClose size={30} />
            </button>
          ) : (
            <button className=" cursor-pointer" onClick={() => setMobileMenuOpen((prev) => !prev)}>
              <FiAlignJustify size={30} />
            </button>
          )}
        </div>
      </div>

      {/* Mobile Navbar */}
      {mobileMenuOpen && (
        <div className="md:hidden w-full px-5 py-4">
          <div className="flex flex-col space-y-4">
            <NavLink to="/" onClick={() => setMobileMenuOpen(false)}>
              <div className="flex items-center">
                <GoHomeFill size={25} />
                <span className="ml-2">Home</span>
              </div>
            </NavLink>
            <NavLink to="/friends" onClick={() => setMobileMenuOpen(false)}>
              <div className="flex items-center">
                <FaUserFriends size={25} />
                <span className="ml-2">Friends</span>
              </div>
            </NavLink>
            <NavLink to="/profile" onClick={() => setMobileMenuOpen(false)}>
              <div className="flex items-center">
                <FaCircleUser size={25} />
                <span className="ml-2">Profile</span>
              </div>
            </NavLink>
            <div className="flex flex-col space-y-2.5">
              <NavLink to="/login" onClick={() => setMobileMenuOpen(false)}>
                <Button text={'Login'} />
              </NavLink>
              <NavLink to="/register" onClick={() => setMobileMenuOpen(false)}>
                <Button text={'Register'} />
              </NavLink>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
