import { BrowserRouter, Route, Routes } from 'react-router';
import Header from './components/Header';
import CreatePost from './pages/CreatePost';
import Home from './pages/Home';
import Friends from './pages/Friends';
import Profile from './pages/Profile';
import Register from './pages/Register';
import Login from './pages/Login';
import OtherProfile from './pages/OtherProfile';
import { AuthContextProvider } from './context/AuthContext';
import { UserContextProvider } from './context/UserContext';
import { ToastContainer } from 'react-toastify';

export default function App() {
  return (
    <BrowserRouter>
      <AuthContextProvider>
        <UserContextProvider>
          <ToastContainer />
          <Header />
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/create-post" element={<CreatePost />} />
            <Route path="/friends" element={<Friends />} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/register" element={<Register />} />
            <Route path="/login" element={<Login />} />
            <Route path="/other-profile/:userId" element={<OtherProfile />} />
          </Routes>
        </UserContextProvider>
      </AuthContextProvider>
    </BrowserRouter>
  );
}
