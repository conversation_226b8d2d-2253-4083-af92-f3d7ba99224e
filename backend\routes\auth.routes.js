import authenticate from '../middlewares/authenticate.js';
import express from 'express';
import { login, logout, register, authenticateUser } from '../controllers/auth.controllers.js';
import { profileUpload } from '../middlewares/multer.middleware.js';

const authRouter = express.Router();

// Route for user registration
authRouter.post('/register', profileUpload.single('profileImage'), register);
// Route for user login
authRouter.post('/login', login);
// Route for user logout
authRouter.post('/logout', authenticate, logout);
// Route for user authentication
authRouter.get('/authenticate', authenticate, authenticateUser);

export default authRouter;
