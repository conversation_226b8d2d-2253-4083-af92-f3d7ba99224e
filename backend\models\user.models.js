import { Schema, model } from 'mongoose';

const userSchema = new Schema({
  fullName: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  profileImage: { type: String },
  userPosts: [
    {
      type: Schema.Types.ObjectId,
      ref: 'Post',
    },
  ],
  following: [
    {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
  ],
  followers: [
    {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
  ],
  about: { type: String, default: '' },
});

const User = model('User', userSchema);

export default User;
