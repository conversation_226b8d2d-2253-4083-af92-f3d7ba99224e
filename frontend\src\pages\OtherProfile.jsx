import { useContext } from 'react';
import { useParams } from 'react-router';
import { UserContext } from '../context/UserContext';

export default function OtherProfile() {
  const { userId } = useParams();

  const { user, otherUser, getOtherProfile, followingOrfollowers } = useContext(UserContext);
  getOtherProfile(userId);

  if (!otherUser) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-gray-500">Loading...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center py-10  min-h-screen">
      <div className="bg-white rounded-lg shadow-md p-8 w-full max-w-md">
        <div className="flex flex-col items-center">
          <img
            className="w-24 h-24 rounded-full object-cover border-4 border-primary"
            src={otherUser?.profileImage || 'https://via.placeholder.com/150'}
            alt="Profile"
          />
          <h2 className="mt-4 text-2xl font-bold text-gray-800">
            {otherUser?.fullName || '<PERSON>e'}
          </h2>
          <p className="text-gray-500">{otherUser?.email}</p>
        </div>
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">About</h3>
          <p className="text-gray-600">{otherUser?.about || 'This user has not set a bio yet.'}</p>
        </div>
        <div className="mt-6 flex justify-between text-center">
          <div>
            <span className="block text-xl font-bold text-gray-800">
              {otherUser?.userPosts.length || '0'}
            </span>
            <span className="text-gray-500 text-sm">Posts</span>
          </div>
          <div>
            <span className="block text-xl font-bold text-gray-800">
              {otherUser?.followers.length || '0'}
            </span>
            <span className="text-gray-500 text-sm">Followers</span>
          </div>
          <div>
            <span className="block text-xl font-bold text-gray-800">
              {otherUser?.following.length || '0'}
            </span>
            <span className="text-gray-500 text-sm">FollowersUser</span>
          </div>
        </div>
        <button
          onClick={() => {
            followingOrfollowers(otherUser?._id);
          }}
          className="mt-8 w-full bg-primary hover:bg-hover text-white font-semibold py-2 rounded transition"
        >
          {user?.following.includes(otherUser._id) ? 'Unfollow' : 'Follow'}
        </button>
      </div>
    </div>
  );
}
