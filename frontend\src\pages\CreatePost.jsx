export default function CreatePost() {
  return (
    <div className="min-h-screen bg-gradient-to-br flex items-center justify-center">
      <div className="w-full max-w-xl mx-auto bg-white rounded-2xl shadow-2xl p-8 flex flex-col gap-6 border border-gray-100">
        <h1 className="text-3xl font-bold text-primary mb-2 text-center">Create Post</h1>

        <div>
          <label className="block text-gray-600 font-medium mb-1" htmlFor="content">
            Content
          </label>
          <textarea
            id="content"
            placeholder="Share your thoughts..."
            className="w-full bg-gray-100 rounded-lg px-5 py-3 outline-none text-base text-gray-700 focus:bg-white border border-gray-200 focus:border-primary transition resize-none"
            rows="5"
          ></textarea>
        </div>

        <div>
          <label className="block text-gray-600 font-medium mb-1" htmlFor="image">
            Image
          </label>
          <input
            id="image"
            type="file"
            accept="image/*"
            className="w-full bg-gray-100 rounded-lg px-5 py-3 outline-none text-base text-gray-700 focus:bg-white border border-gray-200 focus:border-primary transition file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary-dark"
          />
        </div>

        <div className="flex justify-end">
          <button className="bg-primary text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:bg-primary-dark transition text-lg">
            Post
          </button>
        </div>
      </div>
    </div>
  );
}
