import { useContext } from 'react';
import { UserContext } from '../context/UserContext';

export default function Profile() {
  const { user } = useContext(UserContext);

  return (
    <div className="flex flex-col items-center py-10  min-h-screen">
      <div className="bg-white rounded-lg shadow-md p-8 w-full max-w-md">
        <div className="flex flex-col items-center">
          <img
            className="w-24 h-24 rounded-full object-cover border-4 border-primary"
            src={user?.profileImage}
            alt="Profile"
          />
          <h2 className="mt-4 text-2xl font-bold text-gray-800">{user?.fullName}</h2>
          <p className="text-gray-500">{user?.email}</p>
        </div>
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">About</h3>
          <p className="text-gray-600">{user?.about}</p>
        </div>
        <div className="mt-6 flex justify-between text-center">
          <div>
            <span className="block text-xl font-bold text-gray-800">{user?.userPosts.length}</span>
            <span className="text-gray-500 text-sm"> Posts</span>
          </div>
          <div>
            <span className="block text-xl font-bold text-gray-800">{user?.followers.length}</span>
            <span className="text-gray-500 text-sm">Followers</span>
          </div>
          <div>
            <span className="block text-xl font-bold text-gray-800">{user?.following.length}</span>
            <span className="text-gray-500 text-sm">Following</span>
          </div>
        </div>
        <button className="mt-8 w-full bg-primary hover:bg-hover text-white font-semibold py-2 rounded transition">
          Edit Profile
        </button>
      </div>
    </div>
  );
}
