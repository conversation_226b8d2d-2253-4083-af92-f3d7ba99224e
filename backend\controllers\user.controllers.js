import User from '../models/user.models.js';

export const updateProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const { about } = req.body;

    const user = await User.findByIdAndUpdate(
      userId,
      { about },
      { new: true, runValidators: true }
    ).select('-password -__v');

    res.status(200).json({
      message: 'Profile updated successfully',
      user,
    });
  } catch (error) {
    console.error('Error updating profile:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const followingOrfollowers = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id: targetUserId } = req.params;

    // Prevent self-following, no sad lonely loops allowed
    if (userId === targetUserId) {
      return res.status(400).json({ message: 'You cannot follow yourself' });
    }

    const user = await User.findById(userId);
    const targetUser = await User.findById(targetUserId);

    if (!user || !targetUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    const isAlreadyFollowing = user.following.includes(targetUserId);
    const updateOperator = isAlreadyFollowing ? '$pull' : '$push';

    // Update current user's following list
    await User.findByIdAndUpdate(
      userId,
      { [updateOperator]: { following: targetUserId } },
      { new: true }
    );

    // Update target user's followers list
    await User.findByIdAndUpdate(
      targetUserId,
      { [updateOperator]: { followers: userId } },
      { new: true }
    );

    return res.status(200).json({
      message: isAlreadyFollowing ? 'Unfollowed successfully' : 'Followed successfully',
    });
  } catch (error) {
    console.error('Error in followingOrfollowers:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const getOtherProfile = async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findOne(userId).select('-password -__v');
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    return res.status(200).json(user);
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const getCurrentUserProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await User.findById(userId).select('-password -__v');
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    return res.status(200).json(user);
  } catch (error) {
    console.error('Error fetching current user profile:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const getAllUsers = async (req, res) => {
  try {
    const users = await User.find().select('-password -__v');
    if (!users || users.length === 0) {
      return res.status(404).json({ message: 'No users found' });
    }
    return res.status(200).json(users);
  } catch (error) {
    console.error('Error fetching all users:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};
