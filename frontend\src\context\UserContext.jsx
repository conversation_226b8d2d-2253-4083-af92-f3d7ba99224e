import { createContext, useEffect, useState } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

export const UserContext = createContext();

export const UserContextProvider = ({ children }) => {
  const URL = import.meta.env.VITE_BACKEND_URL;
  const [user, setUser] = useState(null);
  const [users, setUsers] = useState([]);
  const [otherUser, setOtherUser] = useState(null);

  const updateProfile = async (userAbout) => {
    try {
      const { data } = await axios.put(`${URL}/user/profile-update`, userAbout, {
        withCredentials: true,
      });
      getAllUsers();
      getCurrentUserProfile();
      getOtherProfile();
      setUser(data.user);
      toast.success(data.message);
    } catch (error) {
      toast.error(error.response.data.message);
    }
  };

  const followingOrfollowers = async (userId) => {
    try {
      const { data } = await axios.post(
        `${URL}/user/follow/${userId}`,
        {},
        {
          withCredentials: true,
        }
      );
      getAllUsers();
      getCurrentUserProfile();
      getOtherProfile();
      toast.success(data.message);
    } catch (error) {
      toast.error(error.response.data.message);
    }
  };

  const getAllUsers = async () => {
    try {
      const { data } = await axios.get(`${URL}/user/users`, {
        withCredentials: true,
      });

      getCurrentUserProfile();
      getOtherProfile();
      setUsers(data);
    } catch (error) {
      toast.error(error.response.data.message);
    }
  };

  const getCurrentUserProfile = async () => {
    try {
      const { data } = await axios.get(`${URL}/user/profile`, {
        withCredentials: true,
      });
      getAllUsers();
      getOtherProfile();
      setUser(data);
    } catch (error) {
      toast.error(error.response.data.message);
    }
  };

  const getOtherProfile = async (userId) => {
    try {
      const { data } = await axios.get(`${URL}/user/profile/${userId}`, {
        withCredentials: true,
      });

      getAllUsers();
      getCurrentUserProfile();

      setOtherUser(data);
    } catch (error) {
      toast.error(error.response.data.message);
    }
  };

  useEffect(() => {
    getAllUsers();
    getCurrentUserProfile();
    getOtherProfile();
  }, []);

  return (
    <UserContext.Provider
      value={{ updateProfile, followingOrfollowers, users, user, otherUser, getOtherProfile }}
    >
      {children}
    </UserContext.Provider>
  );
};
